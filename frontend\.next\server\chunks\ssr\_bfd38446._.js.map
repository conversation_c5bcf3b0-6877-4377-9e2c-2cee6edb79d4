{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/app/%5Blocale%5D/%28marketing%29/features/page.tsx"], "sourcesContent": ["import { getTranslations, setRequestLocale } from 'next-intl/server';\n\ntype IFeaturesProps = {\n  params: Promise<{ locale: string }>;\n};\n\nexport async function generateMetadata(props: IFeaturesProps) {\n  const { locale } = await props.params;\n  const t = await getTranslations({\n    locale,\n    namespace: 'Features',\n  });\n\n  return {\n    title: t('meta_title'),\n    description: t('meta_description'),\n  };\n}\n\nexport default async function Features(props: IFeaturesProps) {\n  const { locale } = await props.params;\n  setRequestLocale(locale);\n  const t = await getTranslations({\n    locale,\n    namespace: 'Features',\n  });\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-white\">\n      {/* Hero Section */}\n      <section className=\"relative py-20 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center\">\n            <h1 className=\"text-hero text-gradient mb-6\">\n              {t('hero_title')}\n            </h1>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto mb-12\">\n              {t('hero_description')}\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Grid */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {/* Browser Extension Features */}\n            <div className=\"card-hover p-8\">\n              <div className=\"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-6\">\n                <svg className=\"w-6 h-6 text-primary-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                {t('extension_title')}\n              </h3>\n              <p className=\"text-gray-600 mb-6\">\n                {t('extension_description')}\n              </p>\n              <ul className=\"space-y-2 text-sm text-gray-600\">\n                <li className=\"flex items-center\">\n                  <svg className=\"w-4 h-4 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  {t('extension_feature_1')}\n                </li>\n                <li className=\"flex items-center\">\n                  <svg className=\"w-4 h-4 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  {t('extension_feature_2')}\n                </li>\n                <li className=\"flex items-center\">\n                  <svg className=\"w-4 h-4 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  {t('extension_feature_3')}\n                </li>\n              </ul>\n            </div>\n\n            {/* Web Portal Features */}\n            <div className=\"card-hover p-8\">\n              <div className=\"w-12 h-12 bg-accent-100 rounded-lg flex items-center justify-center mb-6\">\n                <svg className=\"w-6 h-6 text-accent-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                {t('portal_title')}\n              </h3>\n              <p className=\"text-gray-600 mb-6\">\n                {t('portal_description')}\n              </p>\n              <ul className=\"space-y-2 text-sm text-gray-600\">\n                <li className=\"flex items-center\">\n                  <svg className=\"w-4 h-4 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  {t('portal_feature_1')}\n                </li>\n                <li className=\"flex items-center\">\n                  <svg className=\"w-4 h-4 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  {t('portal_feature_2')}\n                </li>\n                <li className=\"flex items-center\">\n                  <svg className=\"w-4 h-4 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  {t('portal_feature_3')}\n                </li>\n              </ul>\n            </div>\n\n            {/* Integrations Features */}\n            <div className=\"card-hover p-8\">\n              <div className=\"w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center mb-6\">\n                <svg className=\"w-6 h-6 text-success-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                {t('integrations_title')}\n              </h3>\n              <p className=\"text-gray-600 mb-6\">\n                {t('integrations_description')}\n              </p>\n              <ul className=\"space-y-2 text-sm text-gray-600\">\n                <li className=\"flex items-center\">\n                  <svg className=\"w-4 h-4 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  {t('integrations_feature_1')}\n                </li>\n                <li className=\"flex items-center\">\n                  <svg className=\"w-4 h-4 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  {t('integrations_feature_2')}\n                </li>\n                <li className=\"flex items-center\">\n                  <svg className=\"w-4 h-4 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  {t('integrations_feature_3')}\n                </li>\n              </ul>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8 bg-primary-50\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          <h2 className=\"text-section text-gray-900 mb-6\">\n            {t('cta_title')}\n          </h2>\n          <p className=\"text-xl text-gray-600 mb-8\">\n            {t('cta_description')}\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <button className=\"btn-primary\">\n              {t('cta_primary')}\n            </button>\n            <button className=\"btn-outline\">\n              {t('cta_secondary')}\n            </button>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;;;AAMO,eAAe,iBAAiB,KAAqB;IAC1D,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,MAAM;IACrC,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAC9B;QACA,WAAW;IACb;IAEA,OAAO;QACL,OAAO,EAAE;QACT,aAAa,EAAE;IACjB;AACF;AAEe,eAAe,SAAS,KAAqB;IAC1D,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,MAAM;IACrC,CAAA,GAAA,2QAAA,CAAA,mBAAgB,AAAD,EAAE;IACjB,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAC9B;QACA,WAAW;IACb;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;;;;;;;;;;;0BAOX,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAA2B,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAClF,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,8OAAC;wCAAE,WAAU;kDACV,EAAE;;;;;;kDAEL,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAI,WAAU;wDAA8B,MAAK;wDAAe,SAAQ;kEACvE,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAqH,UAAS;;;;;;;;;;;oDAE1J,EAAE;;;;;;;0DAEL,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAI,WAAU;wDAA8B,MAAK;wDAAe,SAAQ;kEACvE,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAqH,UAAS;;;;;;;;;;;oDAE1J,EAAE;;;;;;;0DAEL,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAI,WAAU;wDAA8B,MAAK;wDAAe,SAAQ;kEACvE,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAqH,UAAS;;;;;;;;;;;oDAE1J,EAAE;;;;;;;;;;;;;;;;;;;0CAMT,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAA0B,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjF,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,8OAAC;wCAAE,WAAU;kDACV,EAAE;;;;;;kDAEL,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAI,WAAU;wDAA8B,MAAK;wDAAe,SAAQ;kEACvE,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAqH,UAAS;;;;;;;;;;;oDAE1J,EAAE;;;;;;;0DAEL,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAI,WAAU;wDAA8B,MAAK;wDAAe,SAAQ;kEACvE,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAqH,UAAS;;;;;;;;;;;oDAE1J,EAAE;;;;;;;0DAEL,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAI,WAAU;wDAA8B,MAAK;wDAAe,SAAQ;kEACvE,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAqH,UAAS;;;;;;;;;;;oDAE1J,EAAE;;;;;;;;;;;;;;;;;;;0CAMT,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAA2B,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAClF,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,8OAAC;wCAAE,WAAU;kDACV,EAAE;;;;;;kDAEL,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAI,WAAU;wDAA8B,MAAK;wDAAe,SAAQ;kEACvE,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAqH,UAAS;;;;;;;;;;;oDAE1J,EAAE;;;;;;;0DAEL,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAI,WAAU;wDAA8B,MAAK;wDAAe,SAAQ;kEACvE,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAqH,UAAS;;;;;;;;;;;oDAE1J,EAAE;;;;;;;0DAEL,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAI,WAAU;wDAA8B,MAAK;wDAAe,SAAQ;kEACvE,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAqH,UAAS;;;;;;;;;;;oDAE1J,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASf,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,8OAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;sCAEL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;8CACf,EAAE;;;;;;8CAEL,8OAAC;oCAAO,WAAU;8CACf,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB", "debugId": null}}, {"offset": {"line": 651, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next-intl/dist/esm/development/server/react-server/getTranslations.js"], "sourcesContent": ["import { cache } from 'react';\nimport getConfig from './getConfig.js';\nimport getServerTranslator from './getServerTranslator.js';\n\n// Maintainer note: `getTranslations` has two different call signatures.\n// We need to define these with function overloads, otherwise TypeScript\n// messes up the return type.\n\n// Call signature 1: `getTranslations(namespace)`\n\n// Call signature 2: `getTranslations({locale, namespace})`\n\n// Implementation\nasync function getTranslations(namespaceOrOpts) {\n  let namespace;\n  let locale;\n  if (typeof namespaceOrOpts === 'string') {\n    namespace = namespaceOrOpts;\n  } else if (namespaceOrOpts) {\n    locale = namespaceOrOpts.locale;\n    namespace = namespaceOrOpts.namespace;\n  }\n  const config = await getConfig(locale);\n  return getServerTranslator(config, namespace);\n}\nvar getTranslations$1 = cache(getTranslations);\n\nexport { getTranslations$1 as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,wEAAwE;AACxE,wEAAwE;AACxE,6BAA6B;AAE7B,iDAAiD;AAEjD,2DAA2D;AAE3D,iBAAiB;AACjB,eAAe,gBAAgB,eAAe;IAC5C,IAAI;IACJ,IAAI;IACJ,IAAI,OAAO,oBAAoB,UAAU;QACvC,YAAY;IACd,OAAO,IAAI,iBAAiB;QAC1B,SAAS,gBAAgB,MAAM;QAC/B,YAAY,gBAAgB,SAAS;IACvC;IACA,MAAM,SAAS,MAAM,CAAA,GAAA,oMAAA,CAAA,UAAS,AAAD,EAAE;IAC/B,OAAO,CAAA,GAAA,8MAAA,CAAA,UAAmB,AAAD,EAAE,QAAQ;AACrC;AACA,IAAI,oBAAoB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 695, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 728, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;AAAqC,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAMhG,8BAA8B;IAI5BI,SAASC;;;;;;;;;;;AAIX,cAAc,0CAA0C,iBAAA;IAAE,MAAA,kBAAwB;AAAsB,EAAC,IAAA,OAAA;IAAA;IAAA;QAEzG,YAAA;YAAA;YAAA,mCAA4D;gBAC5D,OAAO,KAAA;oBAAMG,cAAc;oBAAA,GAAIX,mBAAmB;4BAChDY,QAAAA;4BAAAA,GAAY;4BAAA;wCACVC,IAAAA;oCAAAA,CAAMZ,UAAUa;oCAAAA,OAAQ;yCACxBC,MAAM;8CACNC,IAAAA,CAAAA,GAAU;wCAAA,QAAA;4CAAA,IAAA;4CAAA;yCAAA;;uCACV,2CAA2C;;iCAC3CC,YAAY;sCACZC,IAAAA,CAAAA;4BAAAA,CAAU;yBAAA;;yBACVC,UAAU,EAAE;0BACd,QAAA,CAAA;wBAAA,UAAA;4BAAA,IAAA;4BAAA;yBAAA;;mBACAC,UAAU;;iBACRC,YAAYnB;kBACd,QAAA,CAAA;gBAAA,UAAA;oBAAA,IAAA;oBAAA;iBAAA;YACF;SAAA,CAAE", "ignoreList": [0], "debugId": null}}]}