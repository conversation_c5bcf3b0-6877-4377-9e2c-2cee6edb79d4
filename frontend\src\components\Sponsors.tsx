/* eslint-disable react-dom/no-unsafe-target-blank */
import Image from 'next/image';

export const Sponsors = () => (
  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
    <div className="card-hover p-6 flex items-center justify-center group">
      <a
        href="https://clerk.com?utm_source=github&utm_medium=sponsorship&utm_campaign=nextjs-boilerplate"
        target="_blank"
        rel="noopener"
        className="block w-full h-full flex items-center justify-center"
      >
        <Image
          src="/assets/images/clerk-logo-dark.png"
          alt="Clerk – Authentication & User Management for Next.js"
          width={160}
          height={80}
          className="max-w-full h-auto object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300"
        />
      </a>
    </div>

    <div className="card-hover p-6 flex items-center justify-center group">
      <a
        href="https://www.coderabbit.ai?utm_source=next_js_starter&utm_medium=github&utm_campaign=next_js_starter_oss_2025"
        target="_blank"
        rel="noopener"
        className="block w-full h-full flex items-center justify-center"
      >
        <Image
          src="/assets/images/coderabbit-logo-light.svg"
          alt="CodeRabbit"
          width={160}
          height={80}
          className="max-w-full h-auto object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300"
        />
      </a>
    </div>

    <div className="card-hover p-6 flex items-center justify-center group">
      <a
        href="https://sentry.io/for/nextjs/?utm_source=github&utm_medium=paid-community&utm_campaign=general-fy25q1-nextjs&utm_content=github-banner-nextjsboilerplate-logo"
        target="_blank"
        rel="noopener"
        className="block w-full h-full flex items-center justify-center"
      >
        <Image
          src="/assets/images/sentry-dark.png"
          alt="Sentry"
          width={160}
          height={80}
          className="max-w-full h-auto object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300"
        />
      </a>
    </div>

    <div className="card-hover p-6 flex items-center justify-center group">
      <a
        href="https://launch.arcjet.com/Q6eLbRE"
        className="block w-full h-full flex items-center justify-center"
      >
        <Image
          src="/assets/images/arcjet-light.svg"
          alt="Arcjet"
          width={160}
          height={80}
          className="max-w-full h-auto object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300"
        />
      </a>
    </div>

    <div className="card-hover p-6 flex items-center justify-center group">
      <a
        href="https://sevalla.com/"
        className="block w-full h-full flex items-center justify-center"
      >
        <Image
          src="/assets/images/sevalla-light.png"
          alt="Sevalla"
          width={160}
          height={80}
          className="max-w-full h-auto object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300"
        />
      </a>
    </div>

    <div className="card-hover p-6 flex items-center justify-center group">
      <a
        href="https://l.crowdin.com/next-js"
        target="_blank"
        rel="noopener"
        className="block w-full h-full flex items-center justify-center"
      >
        <Image
          src="/assets/images/crowdin-dark.png"
          alt="Crowdin"
          width={160}
          height={80}
          className="max-w-full h-auto object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300"
        />
      </a>
    </div>

    <div className="card-hover p-6 flex items-center justify-center group">
      <a
        href="https://posthog.com/?utm_source=github&utm_medium=sponsorship&utm_campaign=next-js-boilerplate"
        target="_blank"
        rel="noopener"
        className="block w-full h-full flex items-center justify-center"
      >
        <Image
          src="https://posthog.com/brand/posthog-logo.svg"
          alt="PostHog"
          width={160}
          height={80}
          className="max-w-full h-auto object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300"
        />
      </a>
    </div>

    <div className="card-hover p-6 flex items-center justify-center group">
      <a
        href="https://betterstack.com/?utm_source=github&utm_medium=sponsorship&utm_campaign=next-js-boilerplate"
        target="_blank"
        rel="noopener"
        className="block w-full h-full flex items-center justify-center"
      >
        <Image
          src="/assets/images/better-stack-dark.png"
          alt="Better Stack"
          width={160}
          height={80}
          className="max-w-full h-auto object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300"
        />
      </a>
    </div>

    <div className="card-hover p-6 flex items-center justify-center group">
      <a
        href="https://www.checklyhq.com/?utm_source=github&utm_medium=sponsorship&utm_campaign=next-js-boilerplate"
        target="_blank"
        rel="noopener"
        className="block w-full h-full flex items-center justify-center"
      >
        <Image
          src="/assets/images/checkly-logo-light.png"
          alt="Checkly"
          width={160}
          height={80}
          className="max-w-full h-auto object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300"
        />
      </a>
    </div>
  </div>
);
