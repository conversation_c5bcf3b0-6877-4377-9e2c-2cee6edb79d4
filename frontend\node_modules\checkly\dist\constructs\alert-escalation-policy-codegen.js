"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.valueForAlertEscalation = void 0;
const sourcegen_1 = require("../sourcegen");
function valueForAlertEscalation(genfile, escalation) {
    genfile.namedImport('AlertEscalationBuilder', 'checkly/constructs');
    function appendCommonArguments(escalation, builder) {
        if (escalation.reminders) {
            const reminders = escalation.reminders;
            builder.object(builder => {
                if (reminders.amount !== undefined) {
                    builder.number('amount', reminders.amount);
                }
                if (reminders.interval !== undefined) {
                    builder.number('interval', reminders.interval);
                }
            });
        }
        if (escalation.parallelRunFailureThreshold) {
            const threshold = escalation.parallelRunFailureThreshold;
            builder.object(builder => {
                if (threshold.enabled !== undefined) {
                    builder.boolean('enabled', threshold.enabled);
                }
                if (threshold.percentage !== undefined) {
                    builder.number('percentage', threshold.percentage);
                }
            });
        }
    }
    switch (escalation.escalationType) {
        case 'RUN_BASED':
            return (0, sourcegen_1.expr)((0, sourcegen_1.ident)('AlertEscalationBuilder'), builder => {
                builder.member((0, sourcegen_1.ident)('runBasedEscalation'));
                builder.call(builder => {
                    const threshold = escalation.runBasedEscalation?.failedRunThreshold;
                    if (threshold !== undefined) {
                        builder.number(threshold);
                    }
                    appendCommonArguments(escalation, builder);
                });
            });
        case 'TIME_BASED':
            return (0, sourcegen_1.expr)((0, sourcegen_1.ident)('AlertEscalationBuilder'), builder => {
                builder.member((0, sourcegen_1.ident)('timeBasedEscalation'));
                builder.call(builder => {
                    const threshold = escalation.timeBasedEscalation?.minutesFailingThreshold;
                    if (threshold !== undefined) {
                        builder.number(threshold);
                    }
                    appendCommonArguments(escalation, builder);
                });
            });
        default:
            throw new Error(`Unsupported alert escalation type ${escalation.escalationType}`);
    }
}
exports.valueForAlertEscalation = valueForAlertEscalation;
//# sourceMappingURL=alert-escalation-policy-codegen.js.map