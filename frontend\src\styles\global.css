/* Import fonts first */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap');

/* Import TailwindCSS */
@import 'tailwindcss';

/* AppExtera Global Styles */
:root {
    /* AppExtera Brand Colors */
    --color-primary: #0ea5e9;
    --color-primary-dark: #0284c7;
    --color-primary-light: #38bdf8;
    --color-secondary: #64748b;
    --color-accent: #d026d7;
    --color-success: #22c55e;
    --color-warning: #f59e0b;
    --color-error: #ef4444;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #0ea5e9 0%, #d026d7 100%);
    --gradient-secondary: linear-gradient(135deg, #3b82f6 0%, #9333ea 100%);

    /* Shadows */
    --shadow-soft: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
    --shadow-medium: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-large: 0 10px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-colored: 0 10px 40px -10px rgba(14, 165, 233, 0.3);

    /* Typography */
    --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
}

/* Dark mode variables */
@media (prefers-color-scheme: dark) {
    :root {
        --color-primary: #38bdf8;
        --color-primary-dark: #0ea5e9;
    }
}

/* Base styles */
html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-sans);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Custom utility classes */
.gradient-primary {
    background: var(--gradient-primary);
}

.gradient-secondary {
    background: var(--gradient-secondary);
}

.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-soft {
    box-shadow: var(--shadow-soft);
}

.shadow-colored {
    box-shadow: var(--shadow-colored);
}

/* RTL Support */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] .rtl\:text-right {
    text-align: right;
}

[dir="rtl"] .rtl\:text-left {
    text-align: left;
}

/* Modern Button Components */
.btn-primary {
    background-color: var(--color-primary);
    color: white;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    box-shadow: var(--shadow-soft);
}

.btn-primary:hover {
    background-color: var(--color-primary-dark);
    box-shadow: var(--shadow-medium);
}

.btn-primary:focus {
    outline: none;
    background-color: var(--color-primary-dark);
    box-shadow: 0 0 0 2px var(--color-primary), 0 0 0 4px rgba(14, 165, 233, 0.2);
}

.btn-secondary {
    background-color: #f1f5f9;
    color: #0f172a;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    box-shadow: var(--shadow-soft);
}

.btn-secondary:hover {
    background-color: #e2e8f0;
    box-shadow: var(--shadow-medium);
}

.btn-outline {
    background-color: transparent;
    color: var(--color-primary);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    border: 2px solid var(--color-primary);
    transition: all 0.2s ease;
    cursor: pointer;
}

.btn-outline:hover {
    background-color: var(--color-primary);
    color: white;
}

.btn-outline:focus {
    outline: none;
    background-color: var(--color-primary);
    color: white;
    box-shadow: 0 0 0 2px var(--color-primary), 0 0 0 4px rgba(14, 165, 233, 0.2);
}

.btn-gradient {
    background: var(--gradient-primary);
    color: white;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    box-shadow: var(--shadow-colored);
}

.btn-gradient:hover {
    opacity: 0.9;
    box-shadow: var(--shadow-large);
}

.btn-gradient:focus {
    outline: none;
    opacity: 0.9;
    box-shadow: 0 0 0 2px var(--color-primary), 0 0 0 4px rgba(14, 165, 233, 0.2);
}

/* Modern Card Components */
.card {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: var(--shadow-soft);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.card-hover {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: var(--shadow-soft);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    overflow: hidden;
}

.card-hover:hover {
    box-shadow: var(--shadow-medium);
    border-color: #cbd5e1;
}

.card-interactive {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: var(--shadow-soft);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    overflow: hidden;
    cursor: pointer;
}

.card-interactive:hover {
    box-shadow: var(--shadow-medium);
    border-color: #cbd5e1;
    transform: translateY(-4px);
}

.card-gradient {
    background: var(--gradient-primary);
    border-radius: 0.75rem;
    box-shadow: var(--shadow-colored);
    border: none;
    color: white;
    overflow: hidden;
}

/* Animation utilities */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading spinner */
.spinner {
    animation: spin 1s linear infinite;
    border-radius: 50%;
    border: 2px solid #d1d5db;
    border-top-color: var(--color-primary);
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Modern Typography System */
.text-hero {
    font-size: 2.25rem;
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.025em;
}

@media (min-width: 768px) {
    .text-hero {
        font-size: 3rem;
    }
}

@media (min-width: 1024px) {
    .text-hero {
        font-size: 3.75rem;
    }
}

.text-section {
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1.25;
}

@media (min-width: 768px) {
    .text-section {
        font-size: 1.875rem;
    }
}

@media (min-width: 1024px) {
    .text-section {
        font-size: 2.25rem;
    }
}

.text-subsection {
    font-size: 1.25rem;
    font-weight: 600;
    line-height: 1.375;
}

@media (min-width: 768px) {
    .text-subsection {
        font-size: 1.5rem;
    }
}

.text-balance {
    text-wrap: balance;
}

/* Modern Layout Utilities */
.container-wide {
    max-width: 80rem;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (min-width: 640px) {
    .container-wide {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .container-wide {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

.container-narrow {
    max-width: 56rem;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (min-width: 640px) {
    .container-narrow {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .container-narrow {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

.section-padding {
    padding-top: 4rem;
    padding-bottom: 4rem;
}

@media (min-width: 1024px) {
    .section-padding {
        padding-top: 6rem;
        padding-bottom: 6rem;
    }
}

.section-padding-sm {
    padding-top: 3rem;
    padding-bottom: 3rem;
}

@media (min-width: 1024px) {
    .section-padding-sm {
        padding-top: 4rem;
        padding-bottom: 4rem;
    }
}