"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiCheckCodegen = exports.valueForAssertion = void 0;
const codegen_1 = require("./internal/codegen");
const sourcegen_1 = require("../sourcegen");
const check_codegen_1 = require("./check-codegen");
const assertion_codegen_1 = require("./internal/assertion-codegen");
const key_value_pair_codegen_1 = require("./key-value-pair-codegen");
function valueForAssertion(genfile, assertion) {
    genfile.namedImport('AssertionBuilder', 'checkly/constructs');
    switch (assertion.source) {
        case 'STATUS_CODE':
            return (0, assertion_codegen_1.valueForNumericAssertion)('AssertionBuilder', 'statusCode', assertion);
        case 'JSON_BODY':
            return (0, assertion_codegen_1.valueForGeneralAssertion)('AssertionBuilder', 'jsonBody', assertion);
        case 'HEADERS':
            return (0, assertion_codegen_1.valueForGeneralAssertion)('AssertionBuilder', 'headers', assertion);
        case 'TEXT_BODY':
            return (0, assertion_codegen_1.valueForGeneralAssertion)('AssertionBuilder', 'textBody', assertion);
        case 'RESPONSE_TIME':
            return (0, assertion_codegen_1.valueForNumericAssertion)('AssertionBuilder', 'responseTime', assertion);
        default:
            throw new Error(`Unsupported assertion source ${assertion.source}`);
    }
}
exports.valueForAssertion = valueForAssertion;
const construct = 'ApiCheck';
class ApiCheckCodegen extends codegen_1.Codegen {
    describe(resource) {
        return `API Check: ${resource.name}`;
    }
    gencode(logicalId, resource, context) {
        const filePath = context.filePath('resources/api-checks', resource.name, {
            tags: resource.tags,
            isolate: true,
            unique: true,
        });
        const file = this.program.generatedConstructFile(filePath.fullPath);
        file.namedImport(construct, 'checkly/constructs');
        file.section((0, sourcegen_1.expr)((0, sourcegen_1.ident)(construct), builder => {
            builder.new(builder => {
                builder.string(logicalId);
                builder.object(builder => {
                    builder.object('request', builder => {
                        builder.string('url', resource.request.url);
                        builder.string('method', resource.request.method);
                        if (resource.request.ipFamily) {
                            builder.string('ipFamily', resource.request.ipFamily);
                        }
                        if (resource.request.followRedirects === false) {
                            builder.boolean('followRedirects', resource.request.followRedirects);
                        }
                        if (resource.request.skipSSL === true) {
                            builder.boolean('skipSSL', resource.request.skipSSL);
                        }
                        if (resource.request.body !== undefined && resource.request.body !== '') {
                            builder.string('body', resource.request.body);
                        }
                        if (resource.request.bodyType && resource.request.bodyType !== 'NONE') {
                            builder.string('bodyType', resource.request.bodyType);
                        }
                        if (resource.request.headers) {
                            const headers = resource.request.headers;
                            if (headers.length > 0) {
                                builder.array('headers', builder => {
                                    for (const header of headers) {
                                        builder.value((0, key_value_pair_codegen_1.valueForKeyValuePair)(this.program, file, context, header));
                                    }
                                });
                            }
                        }
                        if (resource.request.queryParameters) {
                            const queryParameters = resource.request.queryParameters;
                            if (queryParameters.length > 0) {
                                builder.array('queryParameters', builder => {
                                    for (const param of queryParameters) {
                                        builder.value((0, key_value_pair_codegen_1.valueForKeyValuePair)(this.program, file, context, param));
                                    }
                                });
                            }
                        }
                        if (resource.request.basicAuth) {
                            const basicAuth = resource.request.basicAuth;
                            if (basicAuth.username !== '' && basicAuth.password !== '') {
                                builder.object('basicAuth', builder => {
                                    builder.string('username', basicAuth.username);
                                    builder.string('password', basicAuth.password);
                                });
                            }
                        }
                        if (resource.request.assertions) {
                            const assertions = resource.request.assertions;
                            if (assertions.length > 0) {
                                builder.array('assertions', builder => {
                                    for (const assertion of assertions) {
                                        builder.value(valueForAssertion(file, assertion));
                                    }
                                });
                            }
                        }
                    });
                    if (resource.localSetupScript) {
                        const content = resource.localSetupScript;
                        (0, codegen_1.validateScript)(content);
                        const snippetFiles = context.findScriptSnippetFiles(content);
                        for (const snippetFile of snippetFiles) {
                            const localSnippetFile = this.program.generatedSupportFile(`${file.dirname}/snippets/${snippetFile.basename}`);
                            localSnippetFile.plainImport(localSnippetFile.relativePath(snippetFile));
                        }
                        builder.object('setupScript', builder => {
                            const scriptFile = this.program.staticSupportFile(`${file.dirname}/setup-script`, content);
                            builder.string('entrypoint', file.relativePath(scriptFile));
                        });
                    }
                    else if (resource.setupSnippetId) {
                        const snippetFile = context.lookupAuxiliarySnippetFile(resource.setupSnippetId);
                        if (!snippetFile) {
                            throw new Error(`Setup script refers to snippet #${resource.setupSnippetId} which is missing`);
                        }
                        const scriptFile = this.program.generatedSupportFile(`${file.dirname}/setup-script`);
                        scriptFile.plainImport(scriptFile.relativePath(snippetFile));
                        builder.object('setupScript', builder => {
                            builder.string('entrypoint', file.relativePath(scriptFile));
                        });
                    }
                    if (resource.localTearDownScript) {
                        const content = resource.localTearDownScript;
                        (0, codegen_1.validateScript)(content);
                        const snippetFiles = context.findScriptSnippetFiles(content);
                        for (const snippetFile of snippetFiles) {
                            const aliasFile = this.program.generatedSupportFile(`${file.dirname}/snippets/${snippetFile.basename}`);
                            aliasFile.plainImport(aliasFile.relativePath(snippetFile));
                        }
                        builder.object('tearDownScript', builder => {
                            const scriptFile = this.program.staticSupportFile(`${file.dirname}/teardown-script`, content);
                            builder.string('entrypoint', file.relativePath(scriptFile));
                        });
                    }
                    else if (resource.tearDownSnippetId) {
                        const snippetFile = context.lookupAuxiliarySnippetFile(resource.tearDownSnippetId);
                        if (!snippetFile) {
                            throw new Error(`Teardown script refers to snippet #${resource.tearDownSnippetId} which is missing`);
                        }
                        const scriptFile = this.program.generatedSupportFile(`${file.dirname}/teardown-script`);
                        scriptFile.plainImport(scriptFile.relativePath(snippetFile));
                        builder.object('tearDownScript', builder => {
                            builder.string('entrypoint', file.relativePath(scriptFile));
                        });
                    }
                    if (resource.degradedResponseTime !== undefined) {
                        builder.number('degradedResponseTime', resource.degradedResponseTime);
                    }
                    if (resource.maxResponseTime !== undefined) {
                        builder.number('maxResponseTime', resource.maxResponseTime);
                    }
                    (0, check_codegen_1.buildCheckProps)(this.program, file, builder, resource, context);
                });
            });
        }));
    }
}
exports.ApiCheckCodegen = ApiCheckCodegen;
//# sourceMappingURL=api-check-codegen.js.map